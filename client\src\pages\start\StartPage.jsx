import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button, Progress, Chip, Tabs, Tab } from '@heroui/react';
import ProjectWizard from '../project/ProjectWizard';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import OnboardingFlow from '../../components/onboarding/OnboardingFlow';
import { toast } from 'react-hot-toast';
import onboardingService from '../../services/onboardingService';

/**
 * Enhanced Start Page Component
 *
 * Production-ready start page with comprehensive onboarding flow, tutorial system,
 * and progress tracking. Provides seamless user journey from first visit to project creation.
 */
const StartPage = () => {
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);

  // State management
  const [showProjectCreator, setShowProjectCreator] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [activeTab, setActiveTab] = useState('quick-start');
  const [userProgress, setUserProgress] = useState({
    hasCompletedOnboarding: false,
    hasCreatedProject: false,
    hasCompletedTutorial: false,
    currentStep: 1,
    totalSteps: 4
  });
  const [loading, setLoading] = useState(true);

  // Check user progress and onboarding status
  useEffect(() => {
    const checkUserProgress = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Check onboarding completion
        const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${currentUser.id}`);

        // Check if user has projects (indicates experience)
        const hasProjects = localStorage.getItem(`user_has_projects_${currentUser.id}`);

        // Check tutorial completion
        const hasCompletedTutorial = localStorage.getItem(`tutorial_completed_${currentUser.id}`);

        setUserProgress({
          hasCompletedOnboarding: !!hasCompletedOnboarding,
          hasCreatedProject: !!hasProjects,
          hasCompletedTutorial: !!hasCompletedTutorial,
          currentStep: hasCompletedOnboarding ? (hasProjects ? 3 : 2) : 1,
          totalSteps: 4
        });

        // Show onboarding for new users
        if (!hasCompletedOnboarding) {
          setShowOnboarding(true);
        }

      } catch (error) {
        console.error('Error checking user progress:', error);
        toast.error('Failed to load user progress');
      } finally {
        setLoading(false);
      }
    };

    checkUserProgress();
  }, [currentUser]);

  // Handle onboarding completion
  const handleOnboardingComplete = (result) => {
    setShowOnboarding(false);
    setUserProgress(prev => ({
      ...prev,
      hasCompletedOnboarding: true,
      currentStep: 2
    }));

    if (currentUser) {
      localStorage.setItem(`onboarding_completed_${currentUser.id}`, 'true');
    }

    // Navigate based on onboarding result
    if (result?.type === 'venture_created') {
      navigate(result.redirectTo);
    } else if (result?.userGoal === 'learn') {
      navigate('/learn');
    }
  };

  const handleStartProject = () => {
    setShowProjectCreator(true);
  };

  const handleStartTraining = () => {
    navigate('/learn');
  };

  const handleBackToOptions = () => {
    setShowProjectCreator(false);
  };

  const handleStartOnboarding = () => {
    setShowOnboarding(true);
  };

  // Show onboarding flow for new users
  if (showOnboarding) {
    return (
      <OnboardingFlow
        onComplete={handleOnboardingComplete}
        onCancel={() => setShowOnboarding(false)}
      />
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-default-600">Loading your journey...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 py-12 px-4 sm:px-6 lg:px-8">
      <AnimatePresence mode="wait">
        {!showProjectCreator ? (
          <motion.div
            key="options"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header with Progress */}
            <div className="max-w-6xl mx-auto text-center mb-12">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                  Start Your Journey
                </h1>
                <p className="text-xl text-default-600 leading-relaxed mb-6">
                  {userProgress.hasCompletedOnboarding
                    ? "Welcome back! Continue building your projects and earning royalties."
                    : "Begin your Royaltea experience with guided setup and comprehensive tutorials."
                  }
                </p>

                {/* Progress Indicator */}
                <div className="max-w-md mx-auto mb-8">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-default-600">Your Progress</span>
                    <span className="text-sm text-default-500">
                      {userProgress.currentStep}/{userProgress.totalSteps} Complete
                    </span>
                  </div>
                  <Progress
                    value={(userProgress.currentStep / userProgress.totalSteps) * 100}
                    color="primary"
                    className="mb-2"
                  />
                  <div className="flex justify-between text-xs text-default-500">
                    <span className={userProgress.currentStep >= 1 ? "text-green-600" : ""}>
                      {userProgress.hasCompletedOnboarding ? "✓" : "○"} Setup
                    </span>
                    <span className={userProgress.currentStep >= 2 ? "text-green-600" : ""}>
                      {userProgress.hasCreatedProject ? "✓" : "○"} Project
                    </span>
                    <span className={userProgress.currentStep >= 3 ? "text-green-600" : ""}>
                      {userProgress.hasCompletedTutorial ? "✓" : "○"} Tutorial
                    </span>
                    <span className={userProgress.currentStep >= 4 ? "text-green-600" : ""}>
                      ○ Mastery
                    </span>
                  </div>
                </div>

                {/* Status Chips */}
                <div className="flex justify-center gap-2 mb-8">
                  {userProgress.hasCompletedOnboarding && (
                    <Chip color="success" variant="flat" size="sm">
                      ✓ Onboarding Complete
                    </Chip>
                  )}
                  {userProgress.hasCreatedProject && (
                    <Chip color="primary" variant="flat" size="sm">
                      🚀 Project Creator
                    </Chip>
                  )}
                  {userProgress.hasCompletedTutorial && (
                    <Chip color="secondary" variant="flat" size="sm">
                      🎓 Tutorial Graduate
                    </Chip>
                  )}
                </div>
              </motion.div>
            </div>

            {/* Journey Options with Tabs */}
            <div className="max-w-6xl mx-auto">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                className="w-full"
                classNames={{
                  tabList: "grid w-full grid-cols-3 gap-0 rounded-lg bg-default-100 p-1",
                  cursor: "w-full bg-white shadow-sm",
                  tab: "max-w-fit px-4 h-12",
                  tabContent: "group-data-[selected=true]:text-primary"
                }}
              >
                <Tab
                  key="quick-start"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>🚀</span>
                      <span>Quick Start</span>
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="mt-8"
                  >
                    <div className="grid md:grid-cols-2 gap-8">
                      {/* Create Project Card */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Card className="h-full bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                          <CardBody className="p-8 flex flex-col h-full">
                            <div className="text-center mb-6">
                              <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                                <span className="text-3xl">🚀</span>
                              </div>
                              <h3 className="text-2xl font-bold mb-3">Create Your First Project</h3>
                            </div>
                            <p className="text-center mb-6 flex-grow opacity-90">
                              Launch your venture with our guided project wizard. Set up royalty models,
                              invite collaborators, and start tracking contributions in minutes.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Smart royalty model suggestions</span>
                              </div>
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Team collaboration tools</span>
                              </div>
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Automated contribution tracking</span>
                              </div>
                            </div>
                            <Button
                              className="w-full bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                              variant="bordered"
                              size="lg"
                              onClick={handleStartProject}
                            >
                              Start Project Wizard →
                            </Button>
                          </CardBody>
                        </Card>
                      </motion.div>

                      {/* Learn Platform Card */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Card className="h-full bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                          <CardBody className="p-8 flex flex-col h-full">
                            <div className="text-center mb-6">
                              <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                                <span className="text-3xl">🎓</span>
                              </div>
                              <h3 className="text-2xl font-bold mb-3">Master the Platform</h3>
                            </div>
                            <p className="text-center mb-6 flex-grow opacity-90">
                              Learn Royaltea's powerful features through interactive tutorials.
                              Understand royalty models, contribution tracking, and best practices.
                            </p>
                            <div className="space-y-3 mb-6">
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Interactive tutorials</span>
                              </div>
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Real-world examples</span>
                              </div>
                              <div className="flex items-center text-sm opacity-80">
                                <span className="mr-2">✓</span>
                                <span>Best practice guides</span>
                              </div>
                            </div>
                            <Button
                              className="w-full bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                              variant="bordered"
                              size="lg"
                              onClick={handleStartTraining}
                            >
                              Start Learning →
                            </Button>
                          </CardBody>
                        </Card>
                      </motion.div>
                    </div>
                  </motion.div>
                </Tab>

                <Tab
                  key="guided-setup"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>🎯</span>
                      <span>Guided Setup</span>
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="mt-8"
                  >
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold mb-4">Personalized Setup Experience</h3>
                      <p className="text-lg text-default-600 max-w-2xl mx-auto">
                        Let us guide you through a personalized setup based on your goals and experience level.
                      </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-6">
                      {/* New User Path */}
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardBody className="p-6 text-center">
                          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-2xl">👋</span>
                          </div>
                          <h4 className="text-lg font-bold mb-3">I'm New Here</h4>
                          <p className="text-sm text-default-600 mb-4">
                            Complete onboarding with step-by-step guidance
                          </p>
                          <Button
                            color="primary"
                            variant="flat"
                            className="w-full"
                            onClick={handleStartOnboarding}
                            isDisabled={userProgress.hasCompletedOnboarding}
                          >
                            {userProgress.hasCompletedOnboarding ? "✓ Completed" : "Start Onboarding"}
                          </Button>
                        </CardBody>
                      </Card>

                      {/* Returning User Path */}
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardBody className="p-6 text-center">
                          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                            <span className="text-2xl">🔄</span>
                          </div>
                          <h4 className="text-lg font-bold mb-3">I'm Back</h4>
                          <p className="text-sm text-default-600 mb-4">
                            Continue where you left off or start something new
                          </p>
                          <Button
                            color="success"
                            variant="flat"
                            className="w-full"
                            onClick={() => navigate('/dashboard')}
                          >
                            Go to Dashboard
                          </Button>
                        </CardBody>
                      </Card>

                      {/* Expert User Path */}
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardBody className="p-6 text-center">
                          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-100 flex items-center justify-center">
                            <span className="text-2xl">⚡</span>
                          </div>
                          <h4 className="text-lg font-bold mb-3">I'm an Expert</h4>
                          <p className="text-sm text-default-600 mb-4">
                            Skip to advanced project creation tools
                          </p>
                          <Button
                            color="secondary"
                            variant="flat"
                            className="w-full"
                            onClick={() => navigate('/project/wizard')}
                          >
                            Advanced Setup
                          </Button>
                        </CardBody>
                      </Card>
                    </div>
                  </motion.div>
                </Tab>

                <Tab
                  key="explore"
                  title={
                    <div className="flex items-center space-x-2">
                      <span>🗺️</span>
                      <span>Explore</span>
                    </div>
                  }
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="mt-8"
                  >
                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold mb-4">Discover Royaltea</h3>
                      <p className="text-lg text-default-600 max-w-2xl mx-auto">
                        Explore different areas of the platform and find opportunities that match your interests.
                      </p>
                    </div>

                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                      {[
                        {
                          title: "Browse Projects",
                          description: "Discover active projects looking for contributors",
                          icon: "📊",
                          action: () => navigate('/projects'),
                          color: "from-blue-500 to-cyan-500"
                        },
                        {
                          title: "Mission Board",
                          description: "Find bounties and tasks to earn rewards",
                          icon: "🎯",
                          action: () => navigate('/missions'),
                          color: "from-purple-500 to-pink-500"
                        },
                        {
                          title: "Learning Center",
                          description: "Master platform features and best practices",
                          icon: "📚",
                          action: () => navigate('/learn'),
                          color: "from-green-500 to-emerald-500"
                        },
                        {
                          title: "Community",
                          description: "Connect with other creators and collaborators",
                          icon: "👥",
                          action: () => navigate('/social'),
                          color: "from-orange-500 to-red-500"
                        }
                      ].map((item, index) => (
                        <motion.div
                          key={item.title}
                          whileHover={{ scale: 1.05 }}
                          transition={{ duration: 0.2 }}
                          className="cursor-pointer"
                          onClick={item.action}
                        >
                          <Card className={`h-full bg-gradient-to-br ${item.color} text-white hover:shadow-xl transition-shadow`}>
                            <CardBody className="p-6 text-center">
                              <div className="text-4xl mb-4">{item.icon}</div>
                              <h4 className="text-lg font-bold mb-2">{item.title}</h4>
                              <p className="text-sm opacity-90">{item.description}</p>
                            </CardBody>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </Tab>
              </Tabs>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="creator"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="project-creator-container max-w-6xl mx-auto"
          >
            {/* Enhanced Header for Project Creation */}
            <div className="mb-8">
              <Button
                variant="bordered"
                className="mb-6"
                onClick={handleBackToOptions}
                startContent={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                Back to Start Options
              </Button>

              <div className="text-center mb-6">
                <h2 className="text-3xl font-bold mb-2">Create Your Project</h2>
                <p className="text-lg text-default-600">
                  Follow our guided wizard to set up your project with smart defaults and best practices.
                </p>
              </div>

              {/* Progress indicator for project creation */}
              <div className="max-w-md mx-auto">
                <div className="flex items-center justify-between text-sm text-default-500 mb-2">
                  <span>Project Setup Progress</span>
                  <span>Step 1 of 7</span>
                </div>
                <Progress value={14} color="primary" className="mb-4" />
                <div className="text-xs text-center text-default-400">
                  This wizard will guide you through project basics, team setup, and royalty configuration
                </div>
              </div>
            </div>

            <ProjectWizard
              onComplete={(result) => {
                // Update user progress when project is created
                setUserProgress(prev => ({
                  ...prev,
                  hasCreatedProject: true,
                  currentStep: Math.max(prev.currentStep, 3)
                }));

                if (currentUser) {
                  localStorage.setItem(`user_has_projects_${currentUser.id}`, 'true');
                }

                toast.success('Project created successfully!');

                // Navigate to the new project
                if (result?.redirectTo) {
                  navigate(result.redirectTo);
                } else {
                  navigate('/projects');
                }
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default StartPage;
