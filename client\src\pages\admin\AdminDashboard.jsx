import { useContext, useEffect, useState } from "react";
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Navigate } from "react-router-dom";
import DirectSupabaseRoadmapTracker from "../../components/admin/DirectSupabaseRoadmapTracker";
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import { toast } from "react-hot-toast";
import { Card, CardBody, CardHeader, Button, Chip as Badge, Tabs, Tab, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Progress, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input, Select, SelectItem, Textarea } from "../../components/ui/heroui";
import BugReportModal from "../../components/bugs/BugReportModal";
import UserManagement from "../../components/admin/UserManagement";
import ContentModeration from "../../components/admin/ContentModeration";
import AdminAnalytics from "../../components/admin/AdminAnalytics";
import { useDataSync } from '../../contexts/DataSyncContext';
import ActivityLogsDashboard from "../../components/admin/ActivityLogsDashboard";
import { Map, Bug, Eye, EyeOff, Check, Wrench, CheckCircle, Image, Copy, Download, RotateCcw, Activity } from 'lucide-react';

const AdminDashboard = () => {
  const { currentUser } = useContext(UserContext);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [showComponentPlayground, setShowComponentPlayground] = useState(false);
  const [bugs, setBugs] = useState([]);
  const [bugFilter, setBugFilter] = useState("all");
  const [users, setUsers] = useState({});
  const [loadingBugs, setLoadingBugs] = useState(false);
  const [showBugReportModal, setShowBugReportModal] = useState(false);

  // Enhanced admin state
  const [adminStats, setAdminStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    pendingModeration: 0,
    systemHealth: 100,
    totalProjects: 0,
    totalRevenue: 0,
    monthlyGrowth: 0
  });
  const [systemMetrics, setSystemMetrics] = useState({
    cpu: 45,
    memory: 62,
    disk: 38,
    network: 78,
    database: 92
  });
  const [recentUsers, setRecentUsers] = useState([]);
  const [moderationQueue, setModerationQueue] = useState([]);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Check if the user is an admin in the Supabase database
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          console.error("Error checking admin status:", error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [currentUser]);

  // Load comprehensive admin data
  useEffect(() => {
    if (isAdmin && currentUser) {
      loadAdminData();
    }
  }, [isAdmin, currentUser]);

  const loadAdminData = async () => {
    try {
      // Load user statistics
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, created_at, last_sign_in_at, is_active')
        .order('created_at', { ascending: false });

      if (usersError) throw usersError;

      // Load project statistics
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('id, created_at, total_revenue, status')
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;

      // Load moderation queue
      const { data: moderationData, error: moderationError } = await supabase
        .from('content_moderation')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (moderationError) throw moderationError;

      // Calculate statistics
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const activeUsers = usersData?.filter(user =>
        user.last_sign_in_at && new Date(user.last_sign_in_at) >= sevenDaysAgo
      ).length || 0;

      const totalRevenue = projectsData?.reduce((sum, project) =>
        sum + (project.total_revenue || 0), 0
      ) || 0;

      const recentUsers = usersData?.filter(user =>
        new Date(user.created_at) >= thirtyDaysAgo
      ) || [];

      setAdminStats({
        totalUsers: usersData?.length || 0,
        activeUsers,
        pendingModeration: moderationData?.length || 0,
        systemHealth: 98, // Mock system health
        totalProjects: projectsData?.length || 0,
        totalRevenue,
        monthlyGrowth: 12 // Mock growth percentage
      });

      setRecentUsers(recentUsers.slice(0, 10));
      setModerationQueue(moderationData || []);

    } catch (error) {
      console.error('Error loading admin data:', error);
      toast.error('Failed to load admin data');
    }
  };

  // Fetch bugs when the active tab is "bugs" or when the filter changes
  useEffect(() => {
    if (activeTab === "bugs" && isAdmin) {
      fetchBugs();
    }
  }, [activeTab, bugFilter, isAdmin]);

  // Fetch bugs from the database
  const fetchBugs = async () => {
    try {
      setLoadingBugs(true);

      // Fetch bugs
      let query = supabase
        .from('bug_reports')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply filter if not 'all'
      if (bugFilter !== 'all') {
        query = query.eq('status', bugFilter);
      }

      const { data, error } = await query;

      if (error) throw error;

      setBugs(data || []);

      // Fetch user data for reporters and acknowledgers
      await fetchUserData(data);
    } catch (error) {
      console.error('Error fetching bugs:', error);
      toast.error('Failed to load bug reports');
    } finally {
      setLoadingBugs(false);
    }
  };

  // Fetch user data for bug reporters and acknowledgers
  const fetchUserData = async (bugsData) => {
    if (!bugsData || bugsData.length === 0) return;

    try {
      // Get unique user IDs
      const userIds = [...new Set([
        ...bugsData.map(bug => bug.reported_by).filter(Boolean),
        ...bugsData.map(bug => bug.acknowledged_by).filter(Boolean)
      ])];

      if (userIds.length === 0) return;

      const { data, error } = await supabase
        .from('users')
        .select('id, email, display_name')
        .in('id', userIds);

      if (error) throw error;

      // Create a map of user data
      const userMap = {};
      data.forEach(user => {
        userMap[user.id] = user;
      });

      setUsers(userMap);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Handle bug status update
  const handleStatusUpdate = async (bugId, newStatus) => {
    try {
      const updateData = {
        status: newStatus,
        updated_at: new Date()
      };

      // If acknowledging, set acknowledged_by and acknowledged_at
      if (newStatus === 'acknowledged' && bugs.find(b => b.id === bugId)?.status === 'open') {
        updateData.acknowledged_by = currentUser.id;
        updateData.acknowledged_at = new Date();
      }

      // If fixing, set fixed_at
      if (newStatus === 'fixed') {
        updateData.fixed_at = new Date();
      }

      const { error } = await supabase
        .from('bug_reports')
        .update(updateData)
        .eq('id', bugId);

      if (error) throw error;

      // Update local state
      setBugs(prev => prev.map(bug =>
        bug.id === bugId ? { ...bug, ...updateData } : bug
      ));

      toast.success(`Bug status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating bug status:', error);
      toast.error('Failed to update bug status');
    }
  };

  // Toggle public visibility
  const togglePublicVisibility = async (bugId, currentVisibility) => {
    try {
      const { error } = await supabase
        .from('bug_reports')
        .update({ is_public: !currentVisibility })
        .eq('id', bugId);

      if (error) throw error;

      // Update local state
      setBugs(prev => prev.map(bug =>
        bug.id === bugId ? { ...bug, is_public: !bug.is_public } : bug
      ));

      toast.success(`Bug is now ${!currentVisibility ? 'public' : 'private'}`);
    } catch (error) {
      console.error('Error toggling bug visibility:', error);
      toast.error('Failed to update bug visibility');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get user display name
  const getUserName = (userId) => {
    if (!userId) return '';
    const user = users[userId];
    if (!user) return 'Unknown User';
    return user.display_name || user.email;
  };

  // Copy screenshot to clipboard
  const copyScreenshot = async (screenshotUrl) => {
    try {
      const response = await fetch(screenshotUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);
      toast.success('Screenshot copied to clipboard');
    } catch (error) {
      console.error('Error copying screenshot:', error);
      toast.error('Failed to copy screenshot');
    }
  };

  // Download screenshot
  const downloadScreenshot = (screenshotUrl, fileName) => {
    const link = document.createElement('a');
    link.href = screenshotUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle successful bug report submission
  const handleBugReportSuccess = () => {
    setShowBugReportModal(false);
    // Refresh bugs list if we're on the bugs tab
    if (activeTab === 'bugs') {
      fetchBugs();
    }
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  // If not logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  // If not an admin, show access denied
  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Access Denied</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>You do not have permission to access this page. This area is restricted to administrators only.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If admin, show the admin dashboard
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto space-y-8">

          {/* Enhanced Header */}
          <div className="space-y-4">
            <div>
              <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Comprehensive Admin Dashboard
              </h1>
              <p className="text-muted-foreground text-lg">
                Complete system management, user administration, and analytics
              </p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10">
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{adminStats.totalUsers}</div>
                  <div className="text-sm text-muted-foreground">Total Users</div>
                </CardBody>
              </Card>
              <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10">
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{adminStats.activeUsers}</div>
                  <div className="text-sm text-muted-foreground">Active Users</div>
                </CardBody>
              </Card>
              <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/10">
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">{adminStats.pendingModeration}</div>
                  <div className="text-sm text-muted-foreground">Pending Moderation</div>
                </CardBody>
              </Card>
              <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10">
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">{adminStats.systemHealth}%</div>
                  <div className="text-sm text-muted-foreground">System Health</div>
                </CardBody>
              </Card>
            </div>
          </div>

          {/* Enhanced Tabs */}
          <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab} className="w-full">
            <Tab key="overview" title={
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Overview
              </div>
            }>
              <div className="space-y-6">
                {/* System Metrics */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">System Performance</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                      {Object.entries(systemMetrics).map(([key, value]) => (
                        <div key={key} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="capitalize">{key}</span>
                            <span>{value}%</span>
                          </div>
                          <Progress
                            value={value}
                            color={value > 80 ? "danger" : value > 60 ? "warning" : "success"}
                            className="w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">Recent Users</h3>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-3">
                        {recentUsers.length > 0 ? recentUsers.map((user, index) => (
                          <div key={user.id} className="flex items-center justify-between p-3 bg-default-100 rounded-lg">
                            <div>
                              <div className="font-medium">{user.display_name || user.email}</div>
                              <div className="text-sm text-muted-foreground">
                                Joined {new Date(user.created_at).toLocaleDateString()}
                              </div>
                            </div>
                            <Badge color={user.is_active ? "success" : "default"} variant="flat">
                              {user.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        )) : (
                          <div className="text-center py-4 text-muted-foreground">
                            No recent users
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>

                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">Moderation Queue</h3>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-3">
                        {moderationQueue.length > 0 ? moderationQueue.map((item, index) => (
                          <div key={item.id} className="flex items-center justify-between p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                            <div>
                              <div className="font-medium">{item.content_type}</div>
                              <div className="text-sm text-muted-foreground">
                                {item.reason || 'Pending review'}
                              </div>
                            </div>
                            <Badge color="warning" variant="flat">
                              Pending
                            </Badge>
                          </div>
                        )) : (
                          <div className="text-center py-4 text-muted-foreground">
                            No pending moderation
                          </div>
                        )}
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab key="users" title={
              <div className="flex items-center gap-2">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                User Management
              </div>
            }>
              <UserManagement />
            </Tab>

            <Tab key="moderation" title={
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Content Moderation
              </div>
            }>
              <ContentModeration />
            </Tab>

            <Tab key="analytics" title={
              <div className="flex items-center gap-2">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Analytics
              </div>
            }>
              <AdminAnalytics />
            </Tab>

            <Tab key="roadmap" title={
              <div className="flex items-center gap-2">
                <Map className="h-4 w-4" />
                Roadmap
              </div>
            }>
              <div className="space-y-4">
                <DirectSupabaseRoadmapTracker />
              </div>
            </Tab>
            <Tab key="bugs" title={
              <div className="flex items-center gap-2">
                <Bug className="h-4 w-4" />
                Bug Reports
              </div>
            }>
              <div className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Bug Reports Manager</h3>
                      <p className="text-sm text-muted-foreground">
                        Manage and respond to user-reported bugs
                      </p>
                    </div>
                    <Button
                      onPress={() => setShowBugReportModal(true)}
                      className="flex items-center gap-2"
                    >
                      <Bug className="h-4 w-4" />
                      Report Bug
                    </Button>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="space-y-4">
                    {/* Filter buttons */}
                    <div className="flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant={bugFilter === "all" ? "solid" : "bordered"}
                        onPress={() => setBugFilter("all")}
                      >
                        All ({bugs.length})
                      </Button>
                      <Button
                        size="sm"
                        variant={bugFilter === "open" ? "solid" : "bordered"}
                        onPress={() => setBugFilter("open")}
                      >
                        Open ({bugs.filter(bug => bug.status === 'open').length})
                      </Button>
                      <Button
                        size="sm"
                        variant={bugFilter === "in-progress" ? "solid" : "bordered"}
                        onPress={() => setBugFilter("in-progress")}
                      >
                        In Progress ({bugs.filter(bug => bug.status === 'in-progress').length})
                      </Button>
                      <Button
                        size="sm"
                        variant={bugFilter === "fixed" ? "solid" : "bordered"}
                        onPress={() => setBugFilter("fixed")}
                      >
                        Fixed ({bugs.filter(bug => bug.status === 'fixed').length})
                      </Button>
                    </div>

                    {/* Bugs table */}
                    {loadingBugs ? (
                      <div className="flex justify-center py-8">
                        <LoadingAnimation />
                      </div>
                    ) : (
                      <Table aria-label="Bug reports table">
                        <TableHeader>
                          <TableColumn>TITLE</TableColumn>
                          <TableColumn>STATUS</TableColumn>
                          <TableColumn>PRIORITY</TableColumn>
                          <TableColumn>REPORTER</TableColumn>
                          <TableColumn>CREATED</TableColumn>
                          <TableColumn>ACTIONS</TableColumn>
                        </TableHeader>
                        <TableBody>
                          {filteredBugs.map((bug) => (
                            <TableRow key={bug.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{bug.title}</div>
                                  <div className="text-sm text-muted-foreground truncate max-w-xs">
                                    {bug.description}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  color={
                                    bug.status === 'open' ? 'danger' :
                                    bug.status === 'in-progress' ? 'warning' :
                                    bug.status === 'fixed' ? 'success' : 'default'
                                  }
                                  variant="flat"
                                >
                                  {bug.status}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  color={
                                    bug.priority === 'high' ? 'danger' :
                                    bug.priority === 'medium' ? 'warning' :
                                    'default'
                                  }
                                  variant="flat"
                                >
                                  {bug.priority}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {users[bug.user_id]?.full_name || users[bug.user_id]?.email || 'Unknown'}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {new Date(bug.created_at).toLocaleDateString()}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="bordered"
                                    onPress={() => toggleBugVisibility(bug.id)}
                                    isIconOnly
                                  >
                                    {bug.is_public ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="bordered"
                                    onPress={() => updateBugStatus(bug.id, getNextStatus(bug.status))}
                                    isIconOnly
                                  >
                                    {bug.status === 'fixed' ? <RotateCcw className="h-4 w-4" /> : <Check className="h-4 w-4" />}
                                  </Button>
                                  {bug.screenshot_url && (
                                    <Button
                                      size="sm"
                                      variant="bordered"
                                      onPress={() => downloadScreenshot(bug.screenshot_url, bug.title)}
                                      isIconOnly
                                    >
                                      <Download className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </div>
                </CardBody>
              </Card>
              </div>
            </Tab>
            <Tab key="activity" title={
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Activity Logs
              </div>
            }>
              <ActivityLogsDashboard />
            </Tab>
          </Tabs>
        </div>
      </div>

      {/* Bug Report Modal */}
      <BugReportModal
        isOpen={showBugReportModal}
        onClose={() => setShowBugReportModal(false)}
        onSuccess={handleBugReportSuccess}
      />
    </div>
  );
};

export default AdminDashboard;
